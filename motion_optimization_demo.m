%% R-P-P-R机器人运动优化演示
% 展示紫色臂末端斜直线运动 + 保持水平姿态的优化算法

clear; clc;

fprintf('=== R-P-P-R机器人运动优化演示 ===\n\n');

%% 1. 运动优化的核心思想
fprintf('【运动优化核心思想】\n');
fprintf('1. 末端轨迹：紫色臂末端沿斜直线运动\n');
fprintf('2. 姿态约束：紫色臂始终保持水平（相对于重力方向）\n');
fprintf('3. 平滑运动：使用S曲线插值确保速度连续\n');
fprintf('4. 多目标优化：同时满足位置精度和姿态要求\n\n');

%% 2. 轨迹规划参数
start_pos = [0.8, -0.2, -0.1];   % 起始位置
end_pos = [1.1, 0.3, 0.1];       % 结束位置
trajectory_length = norm(end_pos - start_pos);
tilt_angle = rad2deg(atan2(end_pos(3)-start_pos(3), norm(end_pos(1:2)-start_pos(1:2))));

fprintf('【轨迹规划参数】\n');
fprintf('起始位置: [%.3f, %.3f, %.3f] m\n', start_pos);
fprintf('结束位置: [%.3f, %.3f, %.3f] m\n', end_pos);
fprintf('轨迹长度: %.3f m\n', trajectory_length);
fprintf('轨迹倾斜角: %.1f°\n', tilt_angle);
fprintf('运动方向: %s\n', get_motion_direction(start_pos, end_pos));
fprintf('\n');

%% 3. 运动阶段划分
fprintf('【运动阶段划分】\n');
fprintf('阶段1 (0-2秒): 从当前位置移动到轨迹起点\n');
fprintf('阶段2 (2-10秒): 沿斜直线运动（主要阶段）\n');
fprintf('阶段3 (10-12秒): 在终点保持位置\n\n');

%% 4. S曲线轨迹插值演示
fprintf('【S曲线轨迹插值】\n');
fprintf('时间点    归一化参数s    S曲线参数    位置X      位置Y      位置Z\n');
fprintf('------    -----------    ---------    ------     ------     ------\n');

time_points = [2, 3, 4, 5, 6, 7, 8, 9, 10];  % 直线运动阶段的时间点
for t = time_points
    s = (t - 2) / 8;  % 归一化参数 (0到1)
    s_smooth = 3*s^2 - 2*s^3;  % S曲线平滑
    current_pos = start_pos + s_smooth * (end_pos - start_pos);
    
    fprintf('%4.1fs      %6.3f        %6.3f      %6.3f    %6.3f    %6.3f\n', ...
            t, s, s_smooth, current_pos(1), current_pos(2), current_pos(3));
end
fprintf('\n');

%% 5. 保持水平的算法原理
fprintf('【保持水平算法原理】\n');
fprintf('1. 分析问题：紫色臂的姿态受前面所有关节影响\n');
fprintf('2. 目标函数：最小化紫色臂X轴方向的Z分量\n');
fprintf('3. 约束条件：满足末端位置要求 + 关节限制\n');
fprintf('4. 求解方法：数值优化 + 多目标加权\n\n');

%% 6. 逆运动学优化策略
fprintf('【逆运动学优化策略】\n');
fprintf('策略1: 分层求解\n');
fprintf('  - 先用前3个关节(q1,q2,q3)使末端接近目标位置\n');
fprintf('  - 再用第4个关节(q4)调整紫色臂姿态\n');
fprintf('策略2: 多目标优化\n');
fprintf('  - 位置误差权重: 1.0 (主要目标)\n');
fprintf('  - 姿态误差权重: 0.1 (次要目标)\n');
fprintf('  - 综合误差 = 位置误差 + 0.1 × 姿态误差\n\n');

%% 7. 关键技术点
fprintf('【关键技术点】\n');
fprintf('1. D-H参数建模：准确描述机器人几何关系\n');
fprintf('2. 正运动学：快速计算末端位置和姿态\n');
fprintf('3. 数值优化：使用fmincon求解约束优化问题\n');
fprintf('4. 关节限制：确保所有关节在安全范围内\n');
fprintf('5. 实时计算：每帧50ms内完成逆运动学求解\n\n');

%% 8. 性能指标
fprintf('【预期性能指标】\n');
fprintf('位置精度: < 1mm (0.001m)\n');
fprintf('姿态精度: < 5° (紫色臂倾斜角)\n');
fprintf('计算速度: < 50ms/帧 (20fps动画)\n');
fprintf('运动平滑性: S曲线插值，无速度突变\n');
fprintf('关节安全性: 所有关节在限制范围内\n\n');

%% 9. 与原始运动的对比
fprintf('【与原始运动模式对比】\n');
fprintf('原始模式:\n');
fprintf('  - 各关节独立的正弦/余弦运动\n');
fprintf('  - 末端轨迹复杂且不可预测\n');
fprintf('  - 紫色臂姿态随意变化\n');
fprintf('  - 主要用于展示机器人灵活性\n\n');

fprintf('优化模式:\n');
fprintf('  - 末端沿预定义直线运动\n');
fprintf('  - 轨迹简单直观，便于控制\n');
fprintf('  - 紫色臂保持水平，符合实际应用\n');
fprintf('  - 更接近实际工业应用场景\n\n');

%% 10. 应用场景
fprintf('【实际应用场景】\n');
fprintf('1. 焊接作业：沿直线焊缝移动，焊枪保持水平\n');
fprintf('2. 喷涂作业：直线喷涂，喷枪姿态稳定\n');
fprintf('3. 装配作业：零件沿直线插入，保持正确姿态\n');
fprintf('4. 检测作业：传感器沿直线扫描，保持水平\n');
fprintf('5. 搬运作业：物品直线搬运，防止倾倒\n\n');

fprintf('=== 演示完成 ===\n');
fprintf('运行完整仿真请执行: robot_rppr_optimized.m\n');

%% 辅助函数
function direction_str = get_motion_direction(start_pos, end_pos)
    % 分析运动方向
    delta = end_pos - start_pos;
    
    directions = {};
    if delta(1) > 0.01
        directions{end+1} = '+X';
    elseif delta(1) < -0.01
        directions{end+1} = '-X';
    end
    
    if delta(2) > 0.01
        directions{end+1} = '+Y';
    elseif delta(2) < -0.01
        directions{end+1} = '-Y';
    end
    
    if delta(3) > 0.01
        directions{end+1} = '+Z(向上)';
    elseif delta(3) < -0.01
        directions{end+1} = '-Z(向下)';
    end
    
    if isempty(directions)
        direction_str = '静止';
    else
        direction_str = strjoin(directions, ', ');
    end
end
