%% 4自由度R-P-P-R机器人 - 优化运动模式（斜直线+保持水平）
clear; clc; close all;

%% 机器人参数
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% D-H参数表 - 标准D-H参数 [theta, d, a, alpha]
robot.dh_params = [
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端
];

% 关节类型定义 (1=旋转, 0=平移)
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60°
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180°
];

%% 显示D-H参数表
fprintf('=== 4自由度R-P-P-R机器人 - 优化运动模式 ===\n');
fprintf('运动特点：紫色臂末端斜直线运动 + 保持水平姿态\n\n');
fprintf('D-H参数表:\n');
fprintf('关节 | 类型 |  theta  |    d    |    a    |  alpha  | 说明\n');
fprintf('-----|------|---------|---------|---------|---------|------------------\n');
for i = 1:size(robot.dh_params, 1)
    joint_type_str = {'P', 'R'};
    desc_str = {'蓝色臂(R)', '黄色滑块(P)', '绿色臂(P)', '紫色机械臂(R)'};
    
    fprintf(' %d   |  %s   | %7.3f | %7.3f | %7.3f | %7.3f | %s\n', ...
            i, joint_type_str{robot.joint_types(i)+1}, ...
            robot.dh_params(i,1), robot.dh_params(i,2), robot.dh_params(i,3), robot.dh_params(i,4), ...
            desc_str{i});
end
fprintf('\n');

%% 图形设置
figure('Name', 'R-P-P-R Robot - Optimized Linear Motion', 'NumberTitle', 'off', 'Color', 'w', 'Position', [100, 100, 1400, 900]);
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('4-DOF R-P-P-R Robot - 斜直线运动 + 保持水平姿态');
xlim([-0.2, 1.4]); ylim([-0.8, 0.8]); zlim([-0.8, 0.3]);
camlight; lighting gouraud;

%% 视频设置
video_filename = 'rppr_robot_optimized_linear_motion.mp4';
video_writer = VideoWriter(video_filename, 'MPEG-4');
video_writer.FrameRate = 20;
video_writer.Quality = 95;
open(video_writer);

%% 创建图形对象
unit_cube_vertices = [-0.5 -0.5 -0.5; 0.5 -0.5 -0.5; 0.5 0.5 -0.5; -0.5 0.5 -0.5;
                      -0.5 -0.5 0.5; 0.5 -0.5 0.5; 0.5 0.5 0.5; -0.5 0.5 0.5]';
unit_cube_faces = [1 2 6 5; 2 3 7 6; 3 4 8 7; 4 1 5 8; 1 2 3 4; 5 6 7 8];
initial_vertices = zeros(8, 3);

h_blue_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'b', 'EdgeColor', 'k');
h_yellow_slider = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'y', 'EdgeColor', 'k');
h_green_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'g', 'EdgeColor', 'k');
h_purple_effector = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', [0.6, 0.2, 0.8], 'EdgeColor', 'k');

% 参数显示文本框
h_text = annotation('textbox', [0.75, 0.65, 0.23, 0.32], 'String', 'Ready', 'FontSize', 9, ...
                   'VerticalAlignment', 'top', 'EdgeColor', 'k', 'BackgroundColor', 'w', 'FitBoxToText', 'on');

%% 动画仿真 - 优化的斜直线运动
fprintf('开始优化运动模式仿真和视频录制...\n');
fprintf('运动模式：紫色臂末端斜直线运动 + 保持水平姿态\n');

dt = 0.05;
animation_duration = 12;  % 增加动画时长
frame_count = 0;

% 机器人整体下移偏移量
robot_offset_z = -0.3;  % 下移0.3米

% 定义斜直线轨迹参数
start_pos = [0.8, -0.2, -0.1];   % 起始位置 (相对于机器人基座)
end_pos = [1.1, 0.3, 0.1];       % 结束位置
trajectory_length = norm(end_pos - start_pos);

% 轨迹存储（用于绘制轨迹线）
trajectory_points = [];
max_trajectory_points = 200;

fprintf('轨迹信息：\n');
fprintf('起始位置: [%.3f, %.3f, %.3f]\n', start_pos);
fprintf('结束位置: [%.3f, %.3f, %.3f]\n', end_pos);
fprintf('轨迹长度: %.3f m\n', trajectory_length);
fprintf('轨迹倾斜角: %.1f°\n', rad2deg(atan2(end_pos(3)-start_pos(3), norm(end_pos(1:2)-start_pos(1:2)))));

for t = 0:dt:animation_duration
    % 计算轨迹参数 (0到1的归一化参数)
    if t <= 2
        % 前2秒：从当前位置移动到起始位置
        s = t / 2;
        % 获取当前机器人末端位置作为初始位置
        if t == 0
            [~, T_current] = forward_kinematics_dh([0, 0.2, 0, 0], robot);
            current_pos = T_current(1:3, 4);
        end
        target_pos = current_pos * (1-s) + start_pos * s;
        motion_phase = 1;  % 移动到起始位置阶段
    elseif t <= 10
        % 2-10秒：沿斜直线运动
        s = (t - 2) / 8;  % 8秒完成直线运动
        % 使用平滑的S曲线插值
        s_smooth = 3*s^2 - 2*s^3;  % S曲线，起始和结束速度为0
        target_pos = start_pos + s_smooth * (end_pos - start_pos);
        motion_phase = 2;  % 直线运动阶段
    else
        % 10-12秒：保持在结束位置
        target_pos = end_pos;
        motion_phase = 3;  % 保持位置阶段
    end

    % 计算紫色臂应保持的水平角度
    target_q4 = calculate_horizontal_angle(target_pos, robot);

    % 使用改进的逆运动学求解，考虑末端姿态约束
    q = inverse_kinematics_with_orientation(target_pos, target_q4, robot);

    % 应用关节限制
    q = apply_joint_limits(q, robot);

    % D-H正运动学验证
    [T_matrices, T_end] = forward_kinematics_dh(q, robot);
    actual_end_pos = T_end(1:3, 4);
    actual_end_pos(3) = actual_end_pos(3) + robot_offset_z;  % 应用Z轴偏移

    % 存储轨迹点
    if motion_phase == 2  % 只在直线运动阶段记录轨迹
        trajectory_points = [trajectory_points; actual_end_pos'];
        if size(trajectory_points, 1) > max_trajectory_points
            trajectory_points = trajectory_points(2:end, :);  % 保持固定长度
        end
    end

    % 绘制机器人（带整体下移）
    draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, robot_offset_z);

    % 绘制轨迹线
    if size(trajectory_points, 1) > 1
        plot3(trajectory_points(:,1), trajectory_points(:,2), trajectory_points(:,3), ...
              'r-', 'LineWidth', 2, 'DisplayName', '末端轨迹');
    end

    % 绘制目标位置
    target_pos_offset = target_pos;
    target_pos_offset(3) = target_pos_offset(3) + robot_offset_z;
    plot3(target_pos_offset(1), target_pos_offset(2), target_pos_offset(3), ...
          'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r', 'DisplayName', '目标位置');

    % 计算位置误差和姿态误差
    position_error = norm(target_pos - actual_end_pos + [0, 0, robot_offset_z]);

    % 计算紫色臂的实际角度（相对于水平面）
    T_purple_base = T_matrices{3} * transl(0, robot.L_green, 0);  % 绿色臂末端
    T_purple = T_purple_base * rotx(q(4));
    purple_direction = T_purple(1:3, 1);  % 紫色臂X轴方向
    actual_tilt_angle = rad2deg(asin(purple_direction(3)));  % 相对于水平面的倾斜角

    % 运动阶段描述
    phase_descriptions = {'移动到起始位置', '斜直线运动', '保持结束位置'};

    % 更新参数显示
    param_str = sprintf('优化运动模式 - 斜直线 + 保持水平:\n阶段: %s\n\nq1(绕X轴): %+.1f°\nq2(沿X轴): %.3fm\nq3(沿Y轴): %+.3fm\nq4(绕X轴): %+.1f°\n\n目标位置: [%.3f, %.3f, %.3f]\n实际位置: [%.3f, %.3f, %.3f]\n位置误差: %.4f m\n\n紫色臂倾斜角: %.1f°\n轨迹进度: %.1f%%\n\n帧数: %d',...
                       phase_descriptions{motion_phase}, ...
                       rad2deg(q(1)), q(2), q(3), rad2deg(q(4)), ...
                       target_pos_offset(1), target_pos_offset(2), target_pos_offset(3), ...
                       actual_end_pos(1), actual_end_pos(2), actual_end_pos(3), ...
                       position_error, actual_tilt_angle, ...
                       min(100, max(0, (t-2)/8*100)), frame_count);
    set(h_text, 'String', param_str);

    drawnow;

    % 录制视频帧
    frame = getframe(gcf);
    writeVideo(video_writer, frame);
    frame_count = frame_count + 1;

    pause(dt);
end

% 关闭视频文件
close(video_writer);

fprintf('优化运动模式动画完成！\n');
fprintf('视频已保存为: %s\n', video_filename);
fprintf('总帧数: %d\n', frame_count);

%% 计算保持水平所需的角度
function target_q4 = calculate_horizontal_angle(target_pos, robot)
    % 计算紫色臂保持水平所需的q4角度
    % 这需要考虑前面关节的累积旋转效果

    % 使用数值方法找到使紫色臂水平的q4角度
    % 初始猜测
    q_guess = [0, 0.2, 0, 0];  % 基本配置

    % 优化目标：最小化紫色臂的Z方向分量
    objective = @(q4) abs(get_purple_arm_tilt(target_pos, q4, robot));

    % 在合理范围内搜索最优q4
    q4_range = linspace(-pi/2, pi/2, 100);
    min_tilt = inf;
    best_q4 = 0;

    for q4_test = q4_range
        tilt = objective(q4_test);
        if tilt < min_tilt
            min_tilt = tilt;
            best_q4 = q4_test;
        end
    end

    target_q4 = best_q4;
end

%% 获取紫色臂倾斜角度
function tilt_angle = get_purple_arm_tilt(target_pos, q4, robot)
    % 给定目标位置和q4，计算紫色臂的倾斜角度

    % 先求解前三个关节到达目标位置附近
    q_temp = inverse_kinematics_first_three_joints(target_pos, robot);
    q_full = [q_temp, q4];

    % 计算紫色臂方向
    [T_matrices, ~] = forward_kinematics_dh(q_full, robot);
    if length(T_matrices) >= 3
        T_purple_base = T_matrices{3} * transl(0, robot.L_green, 0);
        T_purple = T_purple_base * rotx(q4);
        purple_direction = T_purple(1:3, 1);  % X轴方向
        tilt_angle = abs(purple_direction(3));  % Z分量的绝对值
    else
        tilt_angle = inf;
    end
end

%% 前三关节逆运动学
function q123 = inverse_kinematics_first_three_joints(target_pos, robot)
    % 求解前三个关节，使末端尽可能接近目标位置

    % 目标位置减去紫色臂长度，得到绿色臂末端应该到达的位置
    green_end_target = target_pos - [robot.L_purple, 0, 0];  % 假设紫色臂沿X轴

    % 初始猜测
    q0 = [0, 0.2, 0];

    % 优化选项
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 500, 'TolFun', 1e-6);

    % 目标函数：最小化绿色臂末端与目标的距离
    objective = @(q) calc_green_end_error(q, green_end_target, robot);

    % 关节限制（前三个关节）
    lb = robot.q_limits(1:3, 1);
    ub = robot.q_limits(1:3, 2);

    % 求解
    [q123, ~] = fmincon(objective, q0, [], [], [], [], lb, ub, [], options);
end

%% 计算绿色臂末端误差
function error = calc_green_end_error(q123, target_pos, robot)
    % 计算绿色臂末端与目标位置的误差

    q_full = [q123, 0];  % q4设为0
    [T_matrices, ~] = forward_kinematics_dh(q_full, robot);

    if length(T_matrices) >= 3
        % 绿色臂末端位置
        green_end_pos = T_matrices{3}(1:3, 4) + T_matrices{3}(1:3, 2) * robot.L_green;
        error = norm(target_pos - green_end_pos);
    else
        error = inf;
    end
end

%% 带姿态约束的逆运动学
function q_solution = inverse_kinematics_with_orientation(target_pos, target_q4, robot)
    % 考虑末端位置和紫色臂姿态的逆运动学求解

    % 初始猜测
    q0 = [0, 0.2, 0, target_q4];

    % 优化选项
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 1000, 'TolFun', 1e-8, 'TolX', 1e-8);

    % 多目标函数：位置误差 + 姿态误差
    objective = @(q) calc_position_orientation_error(q, target_pos, target_q4, robot);

    % 求解
    [q_solution, ~] = fmincon(objective, q0, [], [], [], [], ...
                             robot.q_limits(:,1), robot.q_limits(:,2), [], options);
end

%% 计算位置和姿态综合误差
function error = calc_position_orientation_error(q, target_pos, target_q4, robot)
    % 计算位置误差和姿态误差的加权和

    [~, T_end] = forward_kinematics_dh(q, robot);
    current_pos = T_end(1:3, 4);

    % 位置误差
    position_error = norm(target_pos - current_pos);

    % 姿态误差（q4角度差异）
    orientation_error = abs(q(4) - target_q4);

    % 加权综合误差
    error = position_error + 0.1 * orientation_error;  % 位置权重更高
end

%% D-H正运动学函数
function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    % D-H正运动学计算

    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        % 获取D-H参数
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        % 根据关节类型确定变量
        if joint_types(i) == 1  % 旋转关节
            theta = theta_0 + q(i);  % theta是变量
            d = d_0;                 % d是常数
        else  % 平移关节
            theta = theta_0;         % theta是常数
            d = d_0 + q(i);         % d是变量
        end

        % 计算D-H变换矩阵
        T_i = dh_transform(theta, d, a, alpha);

        % 累积变换
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

%% 机器人绘制函数（带整体偏移）
function draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, offset_z)

    % 整体偏移变换
    T_offset = transl(0, 0, offset_z);

    % 蓝色臂：绕X轴旋转
    T_blue = T_offset * rotx(q(1));
    blue_vertices = transform_part(transl(robot.L_blue/2, 0, 0), ...
                                  diag([robot.L_blue, robot.W_blue, robot.W_blue]), ...
                                  T_blue, unit_cube_vertices);
    set(h_blue_arm, 'Vertices', blue_vertices);

    % 黄色滑块：在蓝色臂上滑动
    T_yellow_base = T_blue;
    T_yellow = T_yellow_base * transl(q(2), 0, robot.W_blue/2 + robot.H_yellow/2);
    yellow_vertices = transform_part(transl(0, 0, 0), ...
                                    diag([robot.L_yellow, robot.W_yellow, robot.H_yellow]), ...
                                    T_yellow, unit_cube_vertices);
    set(h_yellow_slider, 'Vertices', yellow_vertices);

    % 绿色臂：在黄色滑块上方，沿Y轴平移
    T_green_base = T_yellow * transl(0, 0, robot.H_yellow/2 + robot.W_green/2);
    T_green = T_green_base * transl(0, q(3), 0);

    green_vertices = transform_part(transl(0, robot.L_green/2, 0), ...
                                   diag([robot.W_green, robot.L_green, robot.W_green]), ...
                                   T_green, unit_cube_vertices);
    set(h_green_arm, 'Vertices', green_vertices);

    % 紫色机械臂：安装在绿色臂末端，沿X轴方向延伸
    T_green_end = T_green * transl(0, robot.L_green, 0);
    % 绕X轴旋转
    T_purple = T_green_end * rotx(q(4));

    % 紫色机械臂沿X轴方向延伸
    purple_vertices = transform_part(transl(robot.L_purple/2, 0, 0), ...
                                    diag([robot.L_purple, robot.W_purple, robot.H_purple]), ...
                                    T_purple, unit_cube_vertices);
    set(h_purple_effector, 'Vertices', purple_vertices);
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    % 绕X轴旋转变换矩阵
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    % 平移变换矩阵
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end

function V_world = transform_part(T_local_pos, S_local, T_world, V_unit)
    % 变换部件顶点
    V_model_4d = T_local_pos * [S_local*V_unit; ones(1, size(V_unit, 2))];
    V_world_4d = T_world * V_model_4d;
    V_world = V_world_4d(1:3, :)';
end
