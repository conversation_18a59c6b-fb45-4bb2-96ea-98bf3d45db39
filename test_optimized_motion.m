%% 测试优化运动模式 - 简化版本
clear; clc; close all;

%% 机器人参数
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% D-H参数表
robot.dh_params = [
    0,       0,                                     robot.L_blue,   0;
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;
    0,       0,                                     0,              -pi/2;
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;
];

robot.joint_types = [1, 0, 0, 1];  % R-P-P-R

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);
    0,             0.46;
    -green_slide_limit, green_slide_limit;
    -pi,           pi
];

fprintf('=== 测试优化运动模式 ===\n');
fprintf('运动特点：紫色臂末端斜直线运动 + 保持水平姿态\n\n');

%% 测试轨迹规划
% 定义斜直线轨迹
start_pos = [0.8, -0.2, -0.1];
end_pos = [1.1, 0.3, 0.1];
trajectory_length = norm(end_pos - start_pos);

fprintf('轨迹信息：\n');
fprintf('起始位置: [%.3f, %.3f, %.3f]\n', start_pos);
fprintf('结束位置: [%.3f, %.3f, %.3f]\n', end_pos);
fprintf('轨迹长度: %.3f m\n', trajectory_length);
fprintf('轨迹倾斜角: %.1f°\n\n', rad2deg(atan2(end_pos(3)-start_pos(3), norm(end_pos(1:2)-start_pos(1:2)))));

%% 测试几个关键点的逆运动学
test_points = [
    start_pos;
    start_pos + 0.25 * (end_pos - start_pos);
    start_pos + 0.5 * (end_pos - start_pos);
    start_pos + 0.75 * (end_pos - start_pos);
    end_pos
];

fprintf('=== 关键点逆运动学测试 ===\n');
for i = 1:size(test_points, 1)
    target_pos = test_points(i, :);
    fprintf('测试点 %d: [%.3f, %.3f, %.3f]\n', i, target_pos);
    
    % 计算保持水平的角度
    target_q4 = calculate_horizontal_angle(target_pos, robot);
    
    % 逆运动学求解
    q = inverse_kinematics_with_orientation(target_pos, target_q4, robot);
    q = apply_joint_limits(q, robot);
    
    % 验证
    [~, T_end] = forward_kinematics_dh(q, robot);
    actual_pos = T_end(1:3, 4);
    position_error = norm(target_pos - actual_pos);
    
    % 计算紫色臂倾斜角
    [T_matrices, ~] = forward_kinematics_dh(q, robot);
    T_purple_base = T_matrices{3} * transl(0, robot.L_green, 0);
    T_purple = T_purple_base * rotx(q(4));
    purple_direction = T_purple(1:3, 1);
    tilt_angle = rad2deg(asin(purple_direction(3)));
    
    fprintf('  关节角度: [%.1f°, %.3fm, %.3fm, %.1f°]\n', ...
            rad2deg(q(1)), q(2), q(3), rad2deg(q(4)));
    fprintf('  实际位置: [%.3f, %.3f, %.3f]\n', actual_pos);
    fprintf('  位置误差: %.4f m\n', position_error);
    fprintf('  紫色臂倾斜角: %.1f°\n', tilt_angle);
    
    if position_error < 0.01 && abs(tilt_angle) < 5
        fprintf('  ✓ 测试通过\n\n');
    else
        fprintf('  ✗ 测试失败\n\n');
    end
end

fprintf('测试完成！\n');

%% 辅助函数
function target_q4 = calculate_horizontal_angle(target_pos, robot)
    % 简化版本：使用数值搜索找到最佳q4
    q4_range = linspace(-pi/2, pi/2, 50);
    min_tilt = inf;
    best_q4 = 0;
    
    for q4_test = q4_range
        tilt = get_purple_arm_tilt(target_pos, q4_test, robot);
        if tilt < min_tilt
            min_tilt = tilt;
            best_q4 = q4_test;
        end
    end
    
    target_q4 = best_q4;
end

function tilt_angle = get_purple_arm_tilt(target_pos, q4, robot)
    q_temp = inverse_kinematics_first_three_joints(target_pos, robot);
    q_full = [q_temp, q4];
    
    [T_matrices, ~] = forward_kinematics_dh(q_full, robot);
    if length(T_matrices) >= 3
        T_purple_base = T_matrices{3} * transl(0, robot.L_green, 0);
        T_purple = T_purple_base * rotx(q4);
        purple_direction = T_purple(1:3, 1);
        tilt_angle = abs(purple_direction(3));
    else
        tilt_angle = inf;
    end
end

function q123 = inverse_kinematics_first_three_joints(target_pos, robot)
    green_end_target = target_pos - [robot.L_purple, 0, 0];
    q0 = [0, 0.2, 0];
    
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 500, 'TolFun', 1e-6);
    
    objective = @(q) calc_green_end_error(q, green_end_target, robot);
    lb = robot.q_limits(1:3, 1);
    ub = robot.q_limits(1:3, 2);
    
    [q123, ~] = fmincon(objective, q0, [], [], [], [], lb, ub, [], options);
end

function error = calc_green_end_error(q123, target_pos, robot)
    q_full = [q123, 0];
    [T_matrices, ~] = forward_kinematics_dh(q_full, robot);
    
    if length(T_matrices) >= 3
        green_end_pos = T_matrices{3}(1:3, 4) + T_matrices{3}(1:3, 2) * robot.L_green;
        error = norm(target_pos - green_end_pos);
    else
        error = inf;
    end
end

function q_solution = inverse_kinematics_with_orientation(target_pos, target_q4, robot)
    q0 = [0, 0.2, 0, target_q4];
    
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 1000, 'TolFun', 1e-8, 'TolX', 1e-8);
    
    objective = @(q) calc_position_orientation_error(q, target_pos, target_q4, robot);
    
    [q_solution, ~] = fmincon(objective, q0, [], [], [], [], ...
                             robot.q_limits(:,1), robot.q_limits(:,2), [], options);
end

function error = calc_position_orientation_error(q, target_pos, target_q4, robot)
    [~, T_end] = forward_kinematics_dh(q, robot);
    current_pos = T_end(1:3, 4);
    
    position_error = norm(target_pos - current_pos);
    orientation_error = abs(q(4) - target_q4);
    
    error = position_error + 0.1 * orientation_error;
end

function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        if joint_types(i) == 1
            theta = theta_0 + q(i);
            d = d_0;
        else
            theta = theta_0;
            d = d_0 + q(i);
        end

        T_i = dh_transform(theta, d, a, alpha);
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

function q_limited = apply_joint_limits(q, robot)
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end
